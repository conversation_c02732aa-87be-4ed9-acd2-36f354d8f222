import 'dart:math';

/// Kompletní model pro Transportation Hub
class TransportationService {
  final String id;
  final String name;
  final String description;
  final TransportationServiceType serviceType;
  final String? phone;
  final String? website;
  final String? email;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final double rating;
  final int reviewCount;
  final List<String> features;
  final String? operatingHours;
  final String? priceRange;
  final bool isActive;
  final bool hasOnlineBooking;
  final bool acceptsCreditCards;
  final bool hasCustomerSupport;
  final List<String> supportedLanguages;
  final DateTime lastUpdated;

  TransportationService({
    required this.id,
    required this.name,
    required this.description,
    required this.serviceType,
    this.phone,
    this.website,
    this.email,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.rating,
    required this.reviewCount,
    required this.features,
    this.operatingHours,
    this.priceRange,
    required this.isActive,
    required this.hasOnlineBooking,
    required this.acceptsCreditCards,
    required this.hasCustomerSupport,
    required this.supportedLanguages,
    required this.lastUpdated,
  });

  /// Má služba vysoké hodnocení (4.0+)
  bool get isTopRated => rating >= 4.0;

  /// Je dostupná 24/7
  bool get isAvailable24h => operatingHours?.contains('24') ?? false;

  /// Má kompletní služby
  bool get hasFullServices =>
      hasOnlineBooking && acceptsCreditCards && hasCustomerSupport;

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    const double earthRadius = 6371;

    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);

    final double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat) * cos(latitude) * sin(dLng / 2) * sin(dLng / 2);

    final double c = 2 * asin(sqrt(a));

    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }
}

/// Typy dopravních služeb
enum TransportationServiceType {
  carRental, // Autopůjčovna
  parking, // Parkování
  taxi, // Taxi služba
  gasStation, // Čerpací stanice
  carService, // Autoservis
  tollStation, // Mýtná stanice
  restArea, // Odpočívadlo
  carWash, // Myčka aut
}

/// Autopůjčovna
class CarRental {
  final String id;
  final String name;
  final String description;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final String phone;
  final String? website;
  final String? email;
  final double rating;
  final int reviewCount;
  final List<CarCategory> availableCategories;
  final String operatingHours;
  final bool hasAirportPickup;
  final bool hasDelivery;
  final bool hasGPS;
  final bool hasInsurance;
  final int minAge;
  final List<String> requiredDocuments;
  final String? specialOffers;
  final DateTime lastUpdated;

  CarRental({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.phone,
    this.website,
    this.email,
    required this.rating,
    required this.reviewCount,
    required this.availableCategories,
    required this.operatingHours,
    required this.hasAirportPickup,
    required this.hasDelivery,
    required this.hasGPS,
    required this.hasInsurance,
    required this.minAge,
    required this.requiredDocuments,
    this.specialOffers,
    required this.lastUpdated,
  });

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    const double earthRadius = 6371;

    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);

    final double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat) * cos(latitude) * sin(dLng / 2) * sin(dLng / 2);

    final double c = 2 * asin(sqrt(a));

    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }
}

/// Kategorie vozidel
class CarCategory {
  final String id;
  final String name;
  final String description;
  final CarType carType;
  final int seats;
  final int doors;
  final bool hasAirConditioning;
  final bool hasAutomaticTransmission;
  final String fuelType;
  final double dailyPrice;
  final double weeklyPrice;
  final double monthlyPrice;
  final String currency;
  final List<String> includedFeatures;
  final String? imageUrl;

  CarCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.carType,
    required this.seats,
    required this.doors,
    required this.hasAirConditioning,
    required this.hasAutomaticTransmission,
    required this.fuelType,
    required this.dailyPrice,
    required this.weeklyPrice,
    required this.monthlyPrice,
    required this.currency,
    required this.includedFeatures,
    this.imageUrl,
  });
}

/// Typy vozidel
enum CarType {
  economy, // Ekonomická
  compact, // Kompaktní
  midsize, // Střední
  fullsize, // Velká
  luxury, // Luxusní
  suv, // SUV
  van, // Dodávka
  convertible, // Kabriolet
}

/// Parkoviště
class ParkingLot {
  final String id;
  final String name;
  final String description;
  final String address;
  final double latitude;
  final double longitude;
  final String region;
  final ParkingType parkingType;
  final int totalSpaces;
  final int? availableSpaces;
  final double hourlyRate;
  final double dailyRate;
  final double? monthlyRate;
  final String currency;
  final String operatingHours;
  final bool hasElectricCharging;
  final bool hasDisabledAccess;
  final bool hasSecurity;
  final bool hasReservation;
  final String? phone;
  final String? website;
  final double rating;
  final int reviewCount;
  final DateTime lastUpdated;

  ParkingLot({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.parkingType,
    required this.totalSpaces,
    this.availableSpaces,
    required this.hourlyRate,
    required this.dailyRate,
    this.monthlyRate,
    required this.currency,
    required this.operatingHours,
    required this.hasElectricCharging,
    required this.hasDisabledAccess,
    required this.hasSecurity,
    required this.hasReservation,
    this.phone,
    this.website,
    required this.rating,
    required this.reviewCount,
    required this.lastUpdated,
  });

  /// Procento obsazenosti
  double? get occupancyPercentage {
    if (availableSpaces == null) return null;
    return ((totalSpaces - availableSpaces!) / totalSpaces) * 100;
  }

  /// Je parkoviště téměř plné (>90%)
  bool get isNearlyFull {
    final occupancy = occupancyPercentage;
    return occupancy != null && occupancy > 90;
  }
}

/// Typy parkování
enum ParkingType {
  street, // Ulice
  garage, // Garáž
  lot, // Parkoviště
  underground, // Podzemní
  covered, // Krytá
}

/// Taxi služba
class TaxiService {
  final String id;
  final String name;
  final String description;
  final String phone;
  final String? website;
  final String? mobileApp;
  final String region;
  final double rating;
  final int reviewCount;
  final bool hasOnlineBooking;
  final bool hasAppBooking;
  final bool acceptsCreditCards;
  final bool hasAirportService;
  final bool has24hService;
  final String? estimatedWaitTime;
  final double? baseRate;
  final double? perKmRate;
  final String? currency;
  final List<String> serviceTypes;
  final DateTime lastUpdated;

  TaxiService({
    required this.id,
    required this.name,
    required this.description,
    required this.phone,
    this.website,
    this.mobileApp,
    required this.region,
    required this.rating,
    required this.reviewCount,
    required this.hasOnlineBooking,
    required this.hasAppBooking,
    required this.acceptsCreditCards,
    required this.hasAirportService,
    required this.has24hService,
    this.estimatedWaitTime,
    this.baseRate,
    this.perKmRate,
    this.currency,
    required this.serviceTypes,
    required this.lastUpdated,
  });
}

/// Dopravní informace
class TrafficInfo {
  final String id;
  final String title;
  final String description;
  final TrafficInfoType infoType;
  final TrafficSeverity severity;
  final String location;
  final double? latitude;
  final double? longitude;
  final DateTime startTime;
  final DateTime? endTime;
  final List<String> affectedRoutes;
  final String? alternativeRoute;
  final DateTime lastUpdated;

  TrafficInfo({
    required this.id,
    required this.title,
    required this.description,
    required this.infoType,
    required this.severity,
    required this.location,
    this.latitude,
    this.longitude,
    required this.startTime,
    this.endTime,
    required this.affectedRoutes,
    this.alternativeRoute,
    required this.lastUpdated,
  });

  /// Je informace aktuální
  bool get isActive {
    final now = DateTime.now();
    return now.isAfter(startTime) &&
        (endTime == null || now.isBefore(endTime!));
  }
}

/// Typy dopravních informací
enum TrafficInfoType {
  roadClosure, // Uzavírka
  accident, // Nehoda
  construction, // Stavba
  weather, // Počasí
  event, // Událost
  maintenance, // Údržba
}

/// Závažnost dopravní situace
enum TrafficSeverity {
  low, // Nízká
  medium, // Střední
  high, // Vysoká
  critical, // Kritická
}
