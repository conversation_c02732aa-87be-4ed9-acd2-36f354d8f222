import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isSyncing = false;

  Future<void> _syncData() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Data byla úspěšně synchronizována'),
          backgroundColor: Colors.green,
        ),
      );
      setState(() {
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _syncData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ADRIATIC DIARY Header s novým designovým systémem
              Container(
                width: double.infinity,
                height: 350,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(0xFF006994), // Jaderská modrá
                      Color(0xFF2E8B8B), // Středomořská tyrkysová
                      Color(0xFFFF6B35), // Oranžová západ slunce
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    stops: [0.0, 0.6, 1.0],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),
                        // Logo a title
                        Row(
                          children: [
                            // Logo
                            Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.9),
                                borderRadius: BorderRadius.circular(25),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.waves,
                                color: Color(0xFF006994),
                                size: 30,
                              ),
                            ),
                            const SizedBox(width: 16),
                            // Title s Playfair Display
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Adriatic Diary',
                                    style: GoogleFonts.playfairDisplay(
                                      fontSize: 32,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      letterSpacing: 1.5,
                                    ),
                                  ),
                                  Text(
                                    'Explore Croatia with a digital diary',
                                    style: GoogleFonts.inter(
                                      fontSize: 14,
                                      color: Colors.white,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 30),
                        // Phone mockup area podle vzoru
                        Expanded(
                          child: Row(
                            children: [
                              // Calendar section
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'September 2023',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Color(
                                            0xFF006994,
                                          ), // Jaderská modrá
                                        ),
                                      ),
                                      const SizedBox(height: 12),
                                      // Mini calendar grid podle vzoru
                                      Expanded(
                                        child: GridView.builder(
                                          gridDelegate:
                                              const SliverGridDelegateWithFixedCrossAxisCount(
                                                crossAxisCount: 7,
                                                childAspectRatio: 1,
                                              ),
                                          itemCount: 30,
                                          itemBuilder: (context, index) {
                                            final day = index + 1;
                                            final hasMemory = [
                                              5,
                                              12,
                                              18,
                                              25,
                                            ].contains(day);
                                            return Container(
                                              margin: const EdgeInsets.all(1),
                                              decoration: BoxDecoration(
                                                color: hasMemory
                                                    ? const Color(
                                                        0xFFFF6B35,
                                                      ).withValues(
                                                        alpha: 0.2,
                                                      ) // Oranžová západ slunce
                                                    : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  hasMemory
                                                      ? '😊'
                                                      : day.toString(),
                                                  style: TextStyle(
                                                    fontFamily: 'Inter',
                                                    fontSize: hasMemory
                                                        ? 12
                                                        : 10,
                                                    fontWeight: hasMemory
                                                        ? FontWeight.bold
                                                        : FontWeight.normal,
                                                    color: hasMemory
                                                        ? const Color(
                                                            0xFFFF6B35,
                                                          ) // Oranžová západ slunce
                                                        : const Color(
                                                            0xFF006994,
                                                          ), // Jaderská modrá
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              // Map section podle vzoru
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: const Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Interactive travel calendar',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 12,
                                          color: Color(
                                            0xFF2C2C2C,
                                          ), // Dřevěné uhlí
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Map with geotagged memories',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: Color(
                                            0xFF2E8B8B,
                                          ), // Středomořská tyrkysová
                                        ),
                                      ),
                                      SizedBox(height: 16),
                                      Expanded(
                                        child: Center(
                                          child: Icon(
                                            Icons.map,
                                            size: 48,
                                            color: Color(
                                              0xFF006994,
                                            ), // Jaderská modrá
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Content area s paddingem
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Quick Actions s novým designovým systémem
                    Text(
                      'Quick Actions',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF006994), // Jaderská modrá
                      ),
                    ),
                    const SizedBox(height: 20),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildWatercolorQuickAction(
                          context,
                          icon: Icons.camera_alt,
                          label: 'Photo',
                          color: const Color(0xFFFF6B35),
                          onTap: () =>
                              _showFeatureComingSoon(context, 'Camera'),
                        ),
                        _buildWatercolorQuickAction(
                          context,
                          icon: Icons.map,
                          label: 'Map',
                          color: const Color(0xFF006994),
                          onTap: () =>
                              DefaultTabController.of(context).animateTo(1),
                        ),
                        _buildWatercolorQuickAction(
                          context,
                          icon: Icons.book,
                          label: 'Diary',
                          color: const Color(0xFF2E8B8B),
                          onTap: () =>
                              DefaultTabController.of(context).animateTo(6),
                        ),
                        _buildWatercolorQuickAction(
                          context,
                          icon: Icons.place,
                          label: 'Places',
                          color: const Color(0xFFFF6B35),
                          onTap: () =>
                              DefaultTabController.of(context).animateTo(2),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Statistiky s watercolor efektem
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: CustomPaint(
                  painter: WatercolorCardPainter(const Color(0xFF006994)),
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Vaše cesta',
                          style: GoogleFonts.playfairDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF006994),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildWatercolorStatItem(
                              'Navštíveno',
                              '12',
                              'míst',
                              const Color(0xFF2E8B8B),
                            ),
                            _buildWatercolorStatItem(
                              'Ujetých',
                              '245',
                              'km',
                              const Color(0xFF006994),
                            ),
                            _buildWatercolorStatItem(
                              'Fotek',
                              '89',
                              'ks',
                              const Color(0xFFFF6B35),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Doporučení
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Doporučení pro vás',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ListTile(
                        leading: const Icon(Icons.place, color: Colors.blue),
                        title: const Text('Plitvická jezera'),
                        subtitle: const Text(
                          'Národní park s krásnými vodopády',
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () =>
                            DefaultTabController.of(context).animateTo(2),
                      ),
                      ListTile(
                        leading: const Icon(
                          Icons.restaurant,
                          color: Colors.orange,
                        ),
                        title: const Text('Peka'),
                        subtitle: const Text('Tradiční chorvatský pokrm'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () =>
                            DefaultTabController.of(context).animateTo(4),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickAction(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        width: 70,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: color.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Icon(icon, size: 28, color: color),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, String unit) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        Text(unit, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  // Watercolor quick action tlačítko
  Widget _buildWatercolorQuickAction(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        width: 70,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: CustomPaint(
                painter: WatercolorQuickActionPainter(color),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Icon(icon, size: 28, color: color),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Watercolor statistická položka
  Widget _buildWatercolorStatItem(
    String label,
    String value,
    String unit,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: CustomPaint(
        painter: WatercolorStatPainter(color),
        child: Column(
          children: [
            Text(
              value,
              style: GoogleFonts.playfairDisplay(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              unit,
              style: GoogleFonts.inter(
                fontSize: 12,
                color: color.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2C2C2C),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFeatureComingSoon(BuildContext context, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature bude dostupný v příští verzi'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}

// Watercolor painter pro quick action tlačítka
class WatercolorQuickActionPainter extends CustomPainter {
  final Color color;

  WatercolorQuickActionPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh s organickými okraji
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    // Hlavní watercolor tvar
    final path = Path();
    for (int i = 0; i < 360; i += 15) {
      final angle = i * pi / 180;
      final variation = 0.8 + (sin(i * pi / 45) * 0.2); // Organická variace
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    // Základní watercolor vrstva
    paint.color = color.withValues(alpha: 0.2);
    canvas.drawPath(path, paint);

    // Vnitřní vrstva pro hloubku
    final innerPath = Path();
    for (int i = 0; i < 360; i += 20) {
      final angle = i * pi / 180;
      final variation = 0.6 + (cos(i * pi / 30) * 0.15);
      final x = center.dx + (radius * 0.7 * variation) * cos(angle);
      final y = center.dy + (radius * 0.7 * variation) * sin(angle);

      if (i == 0) {
        innerPath.moveTo(x, y);
      } else {
        innerPath.lineTo(x, y);
      }
    }
    innerPath.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(innerPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Watercolor painter pro karty
class WatercolorCardPainter extends CustomPainter {
  final Color color;

  WatercolorCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje karty
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.02,
      size.width * 0.4,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.15,
      size.width * 0.95,
      size.height * 0.05,
    );
    path.lineTo(size.width * 0.98, size.height * 0.9);
    path.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.98,
      size.width * 0.6,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.85,
      size.width * 0.02,
      size.height * 0.95,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.1, size.height * 0.2);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path2.lineTo(size.width * 0.85, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.95,
      size.width * 0.15,
      size.height * 0.85,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Watercolor painter pro statistiky
class WatercolorStatPainter extends CustomPainter {
  final Color color;

  WatercolorStatPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Malý watercolor kruh kolem statistiky
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 3;

    // Organický tvar kolem čísla
    final path = Path();
    for (int i = 0; i < 360; i += 20) {
      final angle = i * pi / 180;
      final variation = 0.7 + (sin(i * pi / 60) * 0.3);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.15);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
