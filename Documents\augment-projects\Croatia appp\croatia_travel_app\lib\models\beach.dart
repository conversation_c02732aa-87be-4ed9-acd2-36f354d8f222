import 'package:json_annotation/json_annotation.dart';

part 'beach.g.dart';

@JsonSerializable()
class Beach {
  final String id;
  final String name;
  final String description;
  final String location;
  final double latitude;
  final double longitude;
  final String region;
  final BeachType beachType;
  final List<String> features;
  final List<String> activities;
  final double rating;
  final int reviewCount;
  final String accessType;
  final bool hasLifeguard;
  final bool hasParking;
  final bool hasRestaurant;
  final bool hasShower;
  final bool hasToilet;
  final bool hasUmbrella;
  final bool hasChair;
  final bool isPetFriendly;
  final bool isNudist;
  final bool isAccessible;
  final List<String> photos;
  final DateTime lastUpdated;

  Beach({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.beachType,
    required this.features,
    required this.activities,
    required this.rating,
    required this.reviewCount,
    required this.accessType,
    required this.hasLifeguard,
    required this.hasParking,
    required this.hasRestaurant,
    required this.hasShower,
    required this.hasToilet,
    required this.hasUmbrella,
    required this.hasChair,
    required this.isPetFriendly,
    required this.isNudist,
    required this.isAccessible,
    required this.photos,
    required this.lastUpdated,
  });

  /// Má pláž vysoké hodnocení (4.5+)
  bool get isTopRated => rating >= 4.5;

  /// Je pláž vhodná pro rodiny
  bool get isFamilyFriendly => hasLifeguard && hasToilet && hasShower && !isNudist;

  /// Má pláž kompletní vybavení
  bool get isFullyEquipped => hasRestaurant && hasShower && hasToilet && hasUmbrella && hasChair;

  /// Vzdálenost od zadané pozice (v km)
  double distanceFrom(double lat, double lng) {
    // Haversine formula pro výpočet vzdálenosti
    const double earthRadius = 6371; // km
    
    final double dLat = _toRadians(latitude - lat);
    final double dLng = _toRadians(longitude - lng);
    
    final double a = 
        (dLat / 2).sin() * (dLat / 2).sin() +
        lat.cos() * latitude.cos() * 
        (dLng / 2).sin() * (dLng / 2).sin();
    
    final double c = 2 * (a.sqrt()).asin();
    
    return earthRadius * c;
  }

  double _toRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }

  factory Beach.fromJson(Map<String, dynamic> json) => _$BeachFromJson(json);
  Map<String, dynamic> toJson() => _$BeachToJson(this);
}

/// Typy pláží
enum BeachType {
  sandy,      // Písečná
  pebble,     // Oblázková
  rocky,      // Skalnatá
  concrete,   // Betonová
  mixed,      // Smíšená
}

/// Regiony Chorvatska pro pláže
enum BeachRegion {
  istria,     // Istrie
  kvarner,    // Kvarner
  dalmatia,   // Dalmácie
  dubrovnik,  // Dubrovník
}

/// Typy přístupu na pláž
enum AccessType {
  free,       // Volný přístup
  paid,       // Placený vstup
  hotel,      // Hotelová pláž
  private,    // Soukromá
}

/// Vodní sporty a aktivity
enum WaterActivity {
  swimming,       // Plavání
  snorkeling,     // Šnorchlování
  diving,         // Potápění
  windsurfing,    // Windsurfing
  kitesurfing,    // Kitesurfing
  sailing,        // Plachtění
  jetski,         // Vodní skútr
  parasailing,    // Parasailing
  fishing,        // Rybaření
  boating,        // Lodní výlety
  kayaking,       // Kajak
  paddleboard,    // SUP
  volleyball,     // Beach volejbal
  waterski,       // Vodní lyže
}

/// Aktuální podmínky na pláži
@JsonSerializable()
class BeachConditions {
  final String beachId;
  final double seaTemperature;
  final double airTemperature;
  final int waveHeight; // cm
  final int windSpeed; // km/h
  final String windDirection;
  final int uvIndex;
  final String weatherCondition;
  final int visibility; // km
  final DateTime timestamp;

  BeachConditions({
    required this.beachId,
    required this.seaTemperature,
    required this.airTemperature,
    required this.waveHeight,
    required this.windSpeed,
    required this.windDirection,
    required this.uvIndex,
    required this.weatherCondition,
    required this.visibility,
    required this.timestamp,
  });

  /// Je počasí ideální pro pláž
  bool get isIdealWeather => 
      seaTemperature >= 20 && 
      airTemperature >= 25 && 
      waveHeight <= 50 && 
      windSpeed <= 20 &&
      !weatherCondition.toLowerCase().contains('rain');

  /// Je vhodné pro vodní sporty
  bool get isGoodForWaterSports => 
      seaTemperature >= 18 && 
      waveHeight <= 100 && 
      windSpeed >= 10 && 
      windSpeed <= 30;

  /// Je vysoký UV index (potřeba ochrany)
  bool get isHighUV => uvIndex >= 6;

  factory BeachConditions.fromJson(Map<String, dynamic> json) => _$BeachConditionsFromJson(json);
  Map<String, dynamic> toJson() => _$BeachConditionsToJson(this);
}

/// Hodnocení pláže od uživatele
@JsonSerializable()
class BeachReview {
  final String id;
  final String beachId;
  final String userId;
  final String userName;
  final double rating;
  final String comment;
  final List<String> photos;
  final DateTime visitDate;
  final List<String> activities;
  final BeachConditions? conditions;
  final DateTime createdAt;

  BeachReview({
    required this.id,
    required this.beachId,
    required this.userId,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.photos,
    required this.visitDate,
    required this.activities,
    this.conditions,
    required this.createdAt,
  });

  factory BeachReview.fromJson(Map<String, dynamic> json) => _$BeachReviewFromJson(json);
  Map<String, dynamic> toJson() => _$BeachReviewToJson(this);
}

/// Oblíbená pláž uživatele
@JsonSerializable()
class FavoriteBeach {
  final String id;
  final String userId;
  final String beachId;
  final DateTime addedAt;
  final String? notes;
  final List<String> preferredActivities;

  FavoriteBeach({
    required this.id,
    required this.userId,
    required this.beachId,
    required this.addedAt,
    this.notes,
    required this.preferredActivities,
  });

  factory FavoriteBeach.fromJson(Map<String, dynamic> json) => _$FavoriteBeachFromJson(json);
  Map<String, dynamic> toJson() => _$FavoriteBeachToJson(this);
}

/// Návštěva pláže
@JsonSerializable()
class BeachVisit {
  final String id;
  final String userId;
  final String beachId;
  final DateTime visitDate;
  final double? userRating;
  final String? notes;
  final List<String> photos;
  final List<String> activities;
  final BeachConditions? conditions;
  final Duration? stayDuration;

  BeachVisit({
    required this.id,
    required this.userId,
    required this.beachId,
    required this.visitDate,
    this.userRating,
    this.notes,
    required this.photos,
    required this.activities,
    this.conditions,
    this.stayDuration,
  });

  factory BeachVisit.fromJson(Map<String, dynamic> json) => _$BeachVisitFromJson(json);
  Map<String, dynamic> toJson() => _$BeachVisitToJson(this);
}

/// Vodní aktivita/sport
@JsonSerializable()
class WaterSport {
  final String id;
  final String name;
  final String description;
  final WaterActivity type;
  final String difficulty; // beginner, intermediate, advanced
  final List<String> equipment;
  final List<String> suitableBeaches;
  final String season; // best season for this activity
  final double averagePrice;
  final String priceUnit; // per hour, per day, per lesson
  final bool requiresLicense;
  final int minAge;
  final List<String> providers;

  WaterSport({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.difficulty,
    required this.equipment,
    required this.suitableBeaches,
    required this.season,
    required this.averagePrice,
    required this.priceUnit,
    required this.requiresLicense,
    required this.minAge,
    required this.providers,
  });

  /// Je aktivita vhodná pro začátečníky
  bool get isBeginnerFriendly => difficulty == 'beginner';

  /// Je aktivita dostupná celoročně
  bool get isYearRound => season.toLowerCase().contains('all') || season.toLowerCase().contains('year');

  factory WaterSport.fromJson(Map<String, dynamic> json) => _$WaterSportFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSportToJson(this);
}
