name: croatia_travel_app
description: "Chorvatská cestovatelská aplikace s offline funkcemi, mapami, AR a mnoho da<PERSON>."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI a navigace
  cupertino_icons: ^1.0.8
  go_router: ^14.6.2
  flutter_bloc: ^8.1.6
  google_fonts: ^6.2.1

  # Mapy a lokace
  google_maps_flutter: ^2.9.0
  google_maps_cluster_manager: ^3.0.0+1
  geolocator: ^13.0.1
  geocoding: ^3.0.0

  # Offline funkcionalita
  sqflite: ^2.4.1
  connectivity_plus: ^6.0.5
  path_provider: ^2.1.4
  dio: ^5.7.0

  # Camera & AR funkce
  camera: ^0.10.5+9
  qr_code_scanner: ^1.0.1
  image_picker: ^1.0.7
  image: ^4.1.7

  # Backend & Cloud
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_messaging: ^14.7.10

  # Personalizace & ML
  flutter_local_notifications: ^17.2.2
  shared_preferences: ^2.3.2

  # Databáze a storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Síť a API
  http: ^1.2.2

  # HTML parsing (pro AI scraping)
  html: ^0.15.4

  # Archive (pro GTFS soubory)
  archive: ^3.4.9

  # Secure storage
  flutter_secure_storage: ^9.0.0

  # Audio a hlasové funkce
  audioplayers: ^6.1.0
  speech_to_text: ^7.0.0
  flutter_tts: ^4.1.0
  record: ^5.1.2

  # Obrázky a média
  cached_network_image: ^3.4.1
  photo_view: ^0.15.0

  # Sdílení a export
  share_plus: ^7.2.2
  pdf: ^3.11.1
  printing: ^5.13.2
  url_launcher: ^6.3.1

  # JSON serialization
  json_annotation: ^4.9.0

  # Utility
  intl: ^0.19.0
  uuid: ^4.5.1
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  battery_plus: ^6.0.3
  path: ^1.9.0

  # Charts a grafy
  fl_chart: ^0.69.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code generation
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/maps/
    - assets/audio/
    - assets/data/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Fonty budou načteny přes Google Fonts package
