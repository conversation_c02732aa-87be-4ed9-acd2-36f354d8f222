import 'package:json_annotation/json_annotation.dart';

part 'emergency_services.g.dart';

// Enums
enum EmergencyType {
  police,
  fire,
  medical,
  mountain,
  sea,
  tourist,
  consulate,
  roadside,
}

enum ServiceAvailability {
  available24h,
  businessHours,
  seasonal,
  emergency,
  unavailable,
}

enum LanguageSupport {
  croatian,
  english,
  german,
  italian,
  french,
  czech,
  slovak,
}

enum UrgencyLevel {
  emergency,
  urgent,
  normal,
  information,
}

// Models
@JsonSerializable()
class EmergencyContact {
  final String id;
  final String name;
  final String nameEn;
  final EmergencyType type;
  final String phoneNumber;
  final String? alternativePhone;
  final String? email;
  final String? website;
  final String address;
  final String addressEn;
  final double latitude;
  final double longitude;
  final String city;
  final String region;
  final ServiceAvailability availability;
  final List<LanguageSupport> languages;
  final String description;
  final String descriptionEn;
  final bool isOfficial;
  final bool isFree;
  final String? specialInstructions;
  final String? specialInstructionsEn;
  final DateTime lastUpdated;

  const EmergencyContact({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.type,
    required this.phoneNumber,
    this.alternativePhone,
    this.email,
    this.website,
    required this.address,
    required this.addressEn,
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.region,
    required this.availability,
    required this.languages,
    required this.description,
    required this.descriptionEn,
    required this.isOfficial,
    required this.isFree,
    this.specialInstructions,
    this.specialInstructionsEn,
    required this.lastUpdated,
  });

  factory EmergencyContact.fromJson(Map<String, dynamic> json) =>
      _$EmergencyContactFromJson(json);
  Map<String, dynamic> toJson() => _$EmergencyContactToJson(this);
}

@JsonSerializable()
class HealthcareProvider {
  final String id;
  final String name;
  final String nameEn;
  final String type; // hospital, clinic, pharmacy, dentist
  final String phoneNumber;
  final String? emergencyPhone;
  final String? email;
  final String? website;
  final String address;
  final String addressEn;
  final double latitude;
  final double longitude;
  final String city;
  final String region;
  final ServiceAvailability availability;
  final List<LanguageSupport> languages;
  final List<String> services;
  final List<String> servicesEn;
  final bool acceptsInsurance;
  final bool acceptsTourists;
  final bool isEmergency;
  final String? specialties;
  final String? specialtiesEn;
  final double? rating;
  final DateTime lastUpdated;

  const HealthcareProvider({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.type,
    required this.phoneNumber,
    this.emergencyPhone,
    this.email,
    this.website,
    required this.address,
    required this.addressEn,
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.region,
    required this.availability,
    required this.languages,
    required this.services,
    required this.servicesEn,
    required this.acceptsInsurance,
    required this.acceptsTourists,
    required this.isEmergency,
    this.specialties,
    this.specialtiesEn,
    this.rating,
    required this.lastUpdated,
  });

  factory HealthcareProvider.fromJson(Map<String, dynamic> json) =>
      _$HealthcareProviderFromJson(json);
  Map<String, dynamic> toJson() => _$HealthcareProviderToJson(this);
}

@JsonSerializable()
class TouristAssistance {
  final String id;
  final String name;
  final String nameEn;
  final String type; // tourist_info, assistance, guide, transport
  final String phoneNumber;
  final String? email;
  final String? website;
  final String address;
  final String addressEn;
  final double latitude;
  final double longitude;
  final String city;
  final String region;
  final ServiceAvailability availability;
  final List<LanguageSupport> languages;
  final List<String> services;
  final List<String> servicesEn;
  final bool isFree;
  final bool isOfficial;
  final String? priceInfo;
  final String? priceInfoEn;
  final double? rating;
  final DateTime lastUpdated;

  const TouristAssistance({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.type,
    required this.phoneNumber,
    this.email,
    this.website,
    required this.address,
    required this.addressEn,
    required this.latitude,
    required this.longitude,
    required this.city,
    required this.region,
    required this.availability,
    required this.languages,
    required this.services,
    required this.servicesEn,
    required this.isFree,
    required this.isOfficial,
    this.priceInfo,
    this.priceInfoEn,
    this.rating,
    required this.lastUpdated,
  });

  factory TouristAssistance.fromJson(Map<String, dynamic> json) =>
      _$TouristAssistanceFromJson(json);
  Map<String, dynamic> toJson() => _$TouristAssistanceToJson(this);
}
