import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/cuisine.dart';
import '../widgets/cuisine_card.dart';
import '../widgets/recipe_detail_widget.dart';

class CuisineScreen extends StatefulWidget {
  const CuisineScreen({super.key});

  @override
  State<CuisineScreen> createState() => _CuisineScreenState();
}

class _CuisineScreenState extends State<CuisineScreen>
    with TickerProviderStateMixin {
  List<CuisineItem> _allCuisineItems = [];
  List<CuisineItem> _filteredItems = [];
  String _selectedRegion = 'all';
  CuisineType? _selectedType;
  bool _vegetarianOnly = false;
  bool _veganOnly = false;
  bool _glutenFreeOnly = false;
  String _searchQuery = '';

  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCuisineData();
  }

  Future<void> _loadCuisineData() async {
    // Simulace načtení dat o chorvatské kuchyni
    _allCuisineItems = [
      CuisineItem(
        id: '1',
        name: 'Peka',
        description:
            'Tradiční způsob přípravy masa a zeleniny pod železným poklopem v ohni.',
        region: 'dalmatia',
        type: CuisineType.mainDish,
        ingredients: [
          'jehněčí maso',
          'brambory',
          'cibule',
          'rozmarýn',
          'olivový olej',
        ],
        recipe:
            'Maso a zeleninu vložte do pekáče, přikryjte železným poklopem a zapečte v ohni...',
        images: ['peka1.jpg', 'peka2.jpg'],
        restaurants: ['rest1', 'rest2'],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        averagePrice: 25.0,
        rating: 4.8,
        tags: ['tradiční', 'Dalmácie', 'maso'],
      ),
      CuisineItem(
        id: '2',
        name: 'Istrijské lanýže',
        description:
            'Vzácné lanýže z istrských lesů, používané v různých pokrmech.',
        region: 'istria',
        type: CuisineType.appetizer,
        ingredients: ['bílé lanýže', 'těstoviny', 'parmazán', 'olivový olej'],
        images: ['truffles1.jpg'],
        restaurants: ['rest3', 'rest4'],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        averagePrice: 45.0,
        rating: 4.9,
        tags: ['lanýže', 'Istrie', 'luxusní'],
      ),
      CuisineItem(
        id: '3',
        name: 'Čevapi',
        description: 'Grilované masové válečky podávané s cibulí a lepinjou.',
        region: 'slavonia',
        type: CuisineType.mainDish,
        ingredients: ['mleté maso', 'cibule', 'lepinja', 'ajvar'],
        images: ['cevapi1.jpg'],
        restaurants: ['rest5', 'rest6'],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        averagePrice: 8.0,
        rating: 4.5,
        tags: ['gril', 'Slavonie', 'rychlé občerstvení'],
      ),
      CuisineItem(
        id: '4',
        name: 'Fritule',
        description: 'Tradiční chorvatské koblihy s rozinkami a rumem.',
        region: 'all',
        type: CuisineType.dessert,
        ingredients: ['mouka', 'vejce', 'rozinky', 'rum', 'cukr'],
        images: ['fritule1.jpg'],
        restaurants: ['rest7', 'rest8'],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        averagePrice: 5.0,
        rating: 4.3,
        tags: ['dezert', 'tradiční', 'sladké'],
      ),
      CuisineItem(
        id: '5',
        name: 'Riba na gradele',
        description: 'Čerstvá ryba grilovaná na dřevěném uhlí.',
        region: 'dalmatia',
        type: CuisineType.seafood,
        ingredients: [
          'čerstvá ryba',
          'olivový olej',
          'česnek',
          'petržel',
          'citron',
        ],
        images: ['grilled_fish1.jpg'],
        restaurants: ['rest9', 'rest10'],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        averagePrice: 20.0,
        rating: 4.7,
        tags: ['ryba', 'gril', 'moře'],
      ),
      CuisineItem(
        id: '6',
        name: 'Maneštra',
        description:
            'Hustá polévka se zeleninou a fazolemi, typická pro Istrii.',
        region: 'istria',
        type: CuisineType.soup,
        ingredients: ['fazole', 'zelí', 'brambory', 'kukuřice', 'uzené maso'],
        images: ['manestra1.jpg'],
        restaurants: ['rest11'],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        averagePrice: 12.0,
        rating: 4.4,
        tags: ['polévka', 'Istrie', 'zimní'],
      ),
    ];

    _filteredItems = List.from(_allCuisineItems);
    _filterItems();
    setState(() {});
  }

  void _filterItems() {
    List<CuisineItem> filtered = List.from(_allCuisineItems);

    // Filtrování podle vyhledávání
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) {
        return item.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            item.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            item.ingredients.any(
              (ingredient) =>
                  ingredient.toLowerCase().contains(_searchQuery.toLowerCase()),
            ) ||
            item.tags.any(
              (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
            );
      }).toList();
    }

    // Filtrování podle regionu
    if (_selectedRegion != 'all') {
      filtered = filtered
          .where(
            (item) => item.region == _selectedRegion || item.region == 'all',
          )
          .toList();
    }

    // Filtrování podle typu
    if (_selectedType != null) {
      filtered = filtered.where((item) => item.type == _selectedType).toList();
    }

    // Dietní filtry
    if (_vegetarianOnly) {
      filtered = filtered.where((item) => item.isVegetarian).toList();
    }
    if (_veganOnly) {
      filtered = filtered.where((item) => item.isVegan).toList();
    }
    if (_glutenFreeOnly) {
      filtered = filtered.where((item) => item.isGlutenFree).toList();
    }

    // Řazení podle hodnocení
    filtered.sort((a, b) => (b.rating ?? 0).compareTo(a.rating ?? 0));

    setState(() {
      _filteredItems = filtered;
    });
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filtry kuchyně',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Region
            DropdownButtonFormField<String>(
              value: _selectedRegion,
              decoration: const InputDecoration(labelText: 'Region'),
              items: const [
                DropdownMenuItem(value: 'all', child: Text('Celé Chorvatsko')),
                DropdownMenuItem(value: 'istria', child: Text('Istrie')),
                DropdownMenuItem(value: 'dalmatia', child: Text('Dalmácie')),
                DropdownMenuItem(value: 'slavonia', child: Text('Slavonie')),
                DropdownMenuItem(value: 'lika', child: Text('Lika')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedRegion = value!;
                });
                _filterItems();
              },
            ),

            const SizedBox(height: 16),

            // Typ pokrmu
            DropdownButtonFormField<CuisineType?>(
              value: _selectedType,
              decoration: const InputDecoration(labelText: 'Typ pokrmu'),
              items: const [
                DropdownMenuItem(value: null, child: Text('Všechny typy')),
                DropdownMenuItem(
                  value: CuisineType.mainDish,
                  child: Text('Hlavní chod'),
                ),
                DropdownMenuItem(
                  value: CuisineType.appetizer,
                  child: Text('Předkrm'),
                ),
                DropdownMenuItem(
                  value: CuisineType.dessert,
                  child: Text('Dezert'),
                ),
                DropdownMenuItem(
                  value: CuisineType.soup,
                  child: Text('Polévka'),
                ),
                DropdownMenuItem(
                  value: CuisineType.seafood,
                  child: Text('Mořské plody'),
                ),
                DropdownMenuItem(
                  value: CuisineType.drink,
                  child: Text('Nápoje'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedType = value;
                });
                _filterItems();
              },
            ),

            const SizedBox(height: 16),

            // Dietní omezení
            const Text(
              'Dietní omezení:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),

            SwitchListTile(
              title: const Text('Vegetariánské'),
              value: _vegetarianOnly,
              onChanged: (value) {
                setState(() {
                  _vegetarianOnly = value;
                  if (value) _veganOnly = false;
                });
                _filterItems();
              },
            ),

            SwitchListTile(
              title: const Text('Veganské'),
              value: _veganOnly,
              onChanged: (value) {
                setState(() {
                  _veganOnly = value;
                  if (value) {
                    _vegetarianOnly = false;
                  }
                });
                _filterItems();
              },
            ),

            SwitchListTile(
              title: const Text('Bezlepkové'),
              value: _glutenFreeOnly,
              onChanged: (value) {
                setState(() {
                  _glutenFreeOnly = value;
                });
                _filterItems();
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chorvatská kuchyně',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFFFF6B35).withValues(alpha: 0.8),
                const Color(0xFF4CAF50).withValues(alpha: 0.7),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorCuisineHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _showFilterDialog,
              icon: const Icon(Icons.filter_list),
              tooltip: 'Filtry',
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(text: 'Všechny', icon: Icon(Icons.restaurant)),
            Tab(text: 'Oblíbené', icon: Icon(Icons.favorite)),
            Tab(text: 'Recepty', icon: Icon(Icons.book)),
            Tab(text: 'Restaurace', icon: Icon(Icons.location_on)),
          ],
        ),
      ),
      body: Container(
        child: CustomPaint(
          painter: WatercolorCuisineBackgroundPainter(),
          child: Column(
            children: [
              // Vyhledávací pole s watercolor
              Container(
                margin: const EdgeInsets.all(16),
                child: CustomPaint(
                  painter: WatercolorCuisineSearchPainter(),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: TextField(
                      controller: _searchController,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: const Color(0xFF2C2C2C),
                      ),
                      decoration: InputDecoration(
                        hintText: 'Vyhledat pokrmy, ingredience...',
                        hintStyle: GoogleFonts.inter(
                          color: const Color(0xFF999999),
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: const Color(0xFFFF6B35),
                        ),
                        suffixIcon: _searchQuery.isNotEmpty
                            ? IconButton(
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchQuery = '';
                                  });
                                  _filterItems();
                                },
                                icon: Icon(
                                  Icons.clear,
                                  color: const Color(0xFF666666),
                                ),
                              )
                            : null,
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                        _filterItems();
                      },
                    ),
                  ),
                ),
              ),

              // Regionální filtry s watercolor
              Container(
                height: 60,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      _buildRegionChip('all', 'Všechny'),
                      _buildRegionChip('dalmatia', 'Dalmácie'),
                      _buildRegionChip('istria', 'Istrie'),
                      _buildRegionChip('slavonia', 'Slavonie'),
                      _buildRegionChip('zagreb', 'Zagreb'),
                    ],
                  ),
                ),
              ),

              // Statistiky s watercolor
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: CustomPaint(
                  painter: WatercolorCuisineStatsPainter(const Color(0xFF4CAF50)),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Text(
                          'Nalezeno: ${_filteredItems.length} pokrmů',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF2C2C2C),
                          ),
                        ),
                        const Spacer(),
                        if (_vegetarianOnly || _veganOnly || _glutenFreeOnly)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _veganOnly
                                  ? 'Vegan'
                                  : _vegetarianOnly
                                  ? 'Vegetarian'
                                  : 'Gluten-free',
                              style: GoogleFonts.inter(
                                color: const Color(0xFF4CAF50),
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),

          const SizedBox(height: 8),

          // Obsah podle tabů
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCuisineList(),
                _buildFavoritesList(),
                _buildRecipesList(),
                _buildRestaurantsList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCuisineList() {
    if (_filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.restaurant_menu, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Žádné pokrmy nenalezeny',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Zkuste změnit filtry nebo vyhledávání',
              style: TextStyle(color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: CuisineCard(
            cuisineItem: item,
            onTap: () => _showCuisineDetail(item),
          ),
        );
      },
    );
  }

  Widget _buildFavoritesList() {
    final favorites = _filteredItems
        .where((item) => (item.rating ?? 0) >= 4.5)
        .toList();

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: favorites.length,
      itemBuilder: (context, index) {
        final item = favorites[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: CuisineCard(
            cuisineItem: item,
            onTap: () => _showCuisineDetail(item),
          ),
        );
      },
    );
  }

  Widget _buildRecipesList() {
    final withRecipes = _filteredItems
        .where((item) => item.recipe != null)
        .toList();

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: withRecipes.length,
      itemBuilder: (context, index) {
        final item = withRecipes[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: CuisineCard(
            cuisineItem: item,
            onTap: () => _showRecipeDetail(item),
            showRecipeIcon: true,
          ),
        );
      },
    );
  }

  Widget _buildRestaurantsList() {
    return const Center(
      child: Text(
        'Mapa restaurací\n(Implementováno v MapScreen)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  void _showCuisineDetail(CuisineItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.name,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(item.description),
            const SizedBox(height: 16),

            if (item.ingredients.isNotEmpty) ...[
              const Text(
                'Ingredience:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: item.ingredients
                    .map((ingredient) => Chip(label: Text(ingredient)))
                    .toList(),
              ),
              const SizedBox(height: 16),
            ],

            if (item.averagePrice != null)
              Text('Průměrná cena: ${item.averagePrice}€'),

            if (item.rating != null)
              Row(
                children: [
                  const Icon(Icons.star, color: Colors.amber),
                  Text(' ${item.rating}'),
                ],
              ),
          ],
        ),
      ),
    );
  }

  void _showRecipeDetail(CuisineItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => RecipeDetailWidget(cuisineItem: item),
    );
  }

  Widget _buildRegionChip(String region, String label) {
    final isSelected = _selectedRegion == region;

    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: CustomPaint(
        painter: WatercolorCuisineRegionChipPainter(
          isSelected ? const Color(0xFFFF6B35) : const Color(0xFF006994),
        ),
        child: GestureDetector(
          onTap: () {
            setState(() {
              _selectedRegion = region;
            });
            _filterItems();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color(0xFFFF6B35).withValues(alpha: 0.1)
                  : Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected
                    ? const Color(0xFFFF6B35)
                    : const Color(0xFF006994).withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected
                    ? const Color(0xFFFF6B35)
                    : const Color(0xFF006994),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }
}

// Watercolor painters pro Cuisine Screen
class WatercolorCuisineHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // Watercolor vlny pro Cuisine header
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.25, size.height * 0.1,
      size.width * 0.5, size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.75, size.height * 0.7,
      size.width, size.height * 0.2,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.5);
    path2.quadraticBezierTo(
      size.width * 0.3, size.height * 0.2,
      size.width * 0.7, size.height * 0.6,
    );
    path2.quadraticBezierTo(
      size.width * 0.9, size.height * 0.8,
      size.width, size.height * 0.4,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFF4CAF50).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCuisineBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro pozadí cuisine
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.05);
    path.quadraticBezierTo(
      size.width * 0.5, size.height * 0.02,
      size.width * 0.9, size.height * 0.08,
    );
    path.lineTo(size.width * 0.95, size.height * 0.95);
    path.quadraticBezierTo(
      size.width * 0.5, size.height * 0.98,
      size.width * 0.05, size.height * 0.92,
    );
    path.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.03);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCuisineSearchPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // Jemný watercolor efekt kolem vyhledávacího pole
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3, size.height * 0.05,
      size.width * 0.7, size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.95, size.height * 0.25,
      size.width * 0.9, size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6, size.height * 0.95,
      size.width * 0.2, size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.02, size.height * 0.5,
      size.width * 0.05, size.height * 0.2,
    );
    path.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCuisineRegionChipPainter extends CustomPainter {
  final Color color;

  WatercolorCuisineRegionChipPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // Malý watercolor efekt kolem region chip
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 3;

    final path = Path();
    for (int i = 0; i < 360; i += 30) {
      final angle = i * pi / 180;
      final variation = 0.7 + (sin(i * pi / 60) * 0.3);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.15);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorCuisineStatsPainter extends CustomPainter {
  final Color color;

  WatercolorCuisineStatsPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // Watercolor okraje pro stats kartu
    final path = Path();
    path.moveTo(size.width * 0.08, size.height * 0.15);
    path.quadraticBezierTo(
      size.width * 0.4, size.height * 0.05,
      size.width * 0.8, size.height * 0.12,
    );
    path.quadraticBezierTo(
      size.width * 0.95, size.height * 0.2,
      size.width * 0.92, size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.6, size.height * 0.95,
      size.width * 0.2, size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.05, size.height * 0.8,
      size.width * 0.08, size.height * 0.15,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.12);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
