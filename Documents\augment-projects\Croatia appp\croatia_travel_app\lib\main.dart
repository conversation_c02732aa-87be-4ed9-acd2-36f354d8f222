import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'views/home_screen.dart';
import 'views/weather_screen.dart';
import 'views/currency_converter_screen.dart';
import 'views/map_screen.dart';
import 'views/places_screen.dart';
import 'views/events_screen.dart';
import 'views/cuisine_screen.dart';
import 'views/budget_screen.dart';
import 'views/diary_screen.dart';
import 'views/dictionary_screen.dart';
import 'views/settings_screen.dart';
import 'views/smart_city_screen.dart';
import 'views/restaurant_discovery_screen.dart';
import 'views/beach_discovery_screen.dart';
import 'views/accommodation_discovery_screen.dart';
import 'views/cultural_discovery_screen.dart';
import 'views/transportation_hub_screen.dart';
import 'views/entertainment_activities_screen.dart';
import 'views/emergency_services_screen.dart';
import 'views/camera_screen.dart';
import 'views/personalization_screen.dart';
import 'views/advanced_settings_screen.dart';
import 'views/ai_assistant_screen.dart';
import 'views/ticket_screen.dart';
import 'views/interactive_map_screen.dart';
import 'views/offline_manager_screen.dart';

void main() {
  runApp(const CroatiaTravelApp());
}

class CroatiaTravelApp extends StatelessWidget {
  const CroatiaTravelApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Croatia Travel App',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF006994), // Jaderská modrá
          brightness: Brightness.light,
        ),
        scaffoldBackgroundColor: const Color(0xFFF8F6F0), // Krémový papír
        cardColor: Colors.white,
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF006994), // Jaderská modrá
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        // ADRIATIC DIARY Typography s Google Fonts
        textTheme: GoogleFonts.interTextTheme().copyWith(
          displayLarge: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2C2C2C), // Dřevěné uhlí
          ),
          displayMedium: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2C2C2C),
          ),
          displaySmall: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2C2C2C),
          ),
          headlineLarge: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF006994), // Jaderská modrá
          ),
          headlineMedium: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF006994),
          ),
          bodyLarge: GoogleFonts.inter(
            color: const Color(0xFF2C2C2C), // Dřevěné uhlí
          ),
          bodyMedium: GoogleFonts.inter(color: const Color(0xFF2C2C2C)),
        ),
        useMaterial3: true,
      ),
      home: const MainNavigationScreen(),
    );
  }
}

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = const [
    HomeScreen(),
    MapScreen(),
    PlacesScreen(),
    EventsScreen(),
    DiaryScreen(),
    BudgetScreen(),
    SettingsScreen(),
  ];

  final List<BottomNavigationBarItem> _navItems = [
    const BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Domů'),
    const BottomNavigationBarItem(icon: Icon(Icons.map), label: 'Mapa'),
    const BottomNavigationBarItem(icon: Icon(Icons.place), label: 'Místa'),
    const BottomNavigationBarItem(icon: Icon(Icons.event), label: 'Události'),
    const BottomNavigationBarItem(icon: Icon(Icons.book), label: 'Deník'),
    const BottomNavigationBarItem(
      icon: Icon(Icons.account_balance_wallet),
      label: 'Rozpočet',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.settings),
      label: 'Nastavení',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'ADRIATIC DIARY',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                const Color(0xFF006994).withValues(alpha: 0.9),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorNavigationPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          // Přístup k dalším funkcím
          PopupMenuButton<String>(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.more_vert),
            ),
            onSelected: (value) {
              switch (value) {
                case 'weather':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const WeatherScreen(),
                    ),
                  );
                  break;
                case 'currency':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CurrencyConverterScreen(),
                    ),
                  );
                  break;
                case 'cuisine':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CuisineScreen(),
                    ),
                  );
                  break;
                case 'dictionary':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DictionaryScreen(),
                    ),
                  );
                  break;
                case 'restaurants':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const RestaurantDiscoveryScreen(),
                    ),
                  );
                  break;
                case 'beaches':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const BeachDiscoveryScreen(),
                    ),
                  );
                  break;
                case 'accommodation':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          const AccommodationDiscoveryScreen(),
                    ),
                  );
                  break;
                case 'culture':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CulturalDiscoveryScreen(),
                    ),
                  );
                  break;
                case 'transport':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TransportationHubScreen(),
                    ),
                  );
                  break;
                case 'entertainment':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          const EntertainmentActivitiesScreen(),
                    ),
                  );
                  break;
                case 'smart_city':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SmartCityScreen(),
                    ),
                  );
                  break;
                case 'emergency':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const EmergencyServicesScreen(),
                    ),
                  );
                  break;
                case 'ai_assistant':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AIAssistantScreen(),
                    ),
                  );
                  break;
                case 'tickets':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TicketScreen(),
                    ),
                  );
                  break;
                case 'interactive_map':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const InteractiveMapScreen(),
                    ),
                  );
                  break;
                case 'offline_manager':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const OfflineManagerScreen(),
                    ),
                  );
                  break;
                case 'camera':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CameraScreen(),
                    ),
                  );
                  break;
                case 'personalization':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PersonalizationScreen(),
                    ),
                  );
                  break;
                case 'advanced_settings':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AdvancedSettingsScreen(),
                    ),
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'weather',
                child: Row(
                  children: [
                    Icon(Icons.wb_sunny, color: Color(0xFFFFB74D)),
                    SizedBox(width: 12),
                    Text('Počasí'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'currency',
                child: Row(
                  children: [
                    Icon(Icons.currency_exchange, color: Color(0xFF4CAF50)),
                    SizedBox(width: 12),
                    Text('Měnový převodník'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'cuisine',
                child: Row(
                  children: [
                    Icon(Icons.restaurant, color: Color(0xFF006994)),
                    SizedBox(width: 12),
                    Text('Kuchyně'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'restaurants',
                child: Row(
                  children: [
                    Icon(Icons.restaurant_menu, color: Color(0xFFFF6B35)),
                    SizedBox(width: 12),
                    Text('Restaurace'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'beaches',
                child: Row(
                  children: [
                    Icon(Icons.beach_access, color: Color(0xFF006994)),
                    SizedBox(width: 12),
                    Text('Pláže'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'accommodation',
                child: Row(
                  children: [
                    Icon(Icons.hotel, color: Color(0xFFFF6B35)),
                    SizedBox(width: 12),
                    Text('Ubytování'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'culture',
                child: Row(
                  children: [
                    Icon(Icons.account_balance, color: Color(0xFFB8860B)),
                    SizedBox(width: 12),
                    Text('Kultura'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'transport',
                child: Row(
                  children: [
                    Icon(Icons.directions_car, color: Color(0xFF1976D2)),
                    SizedBox(width: 12),
                    Text('Doprava'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'entertainment',
                child: Row(
                  children: [
                    Icon(Icons.attractions, color: Color(0xFFE91E63)),
                    SizedBox(width: 12),
                    Text('Zábava'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'dictionary',
                child: Row(
                  children: [
                    Icon(Icons.translate, color: Color(0xFF006994)),
                    SizedBox(width: 12),
                    Text('Slovník'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'smart_city',
                child: Row(
                  children: [
                    Icon(Icons.location_city, color: Color(0xFF006994)),
                    SizedBox(width: 12),
                    Text('Smart City'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'emergency',
                child: Row(
                  children: [
                    Icon(Icons.emergency, color: Color(0xFFDC143C)),
                    SizedBox(width: 12),
                    Text('Nouzové služby'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'ai_assistant',
                child: Row(
                  children: [
                    Icon(Icons.smart_toy, color: Color(0xFF9C27B0)),
                    SizedBox(width: 12),
                    Text('AI Asistent'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'tickets',
                child: Row(
                  children: [
                    Icon(Icons.confirmation_number, color: Color(0xFF4CAF50)),
                    SizedBox(width: 12),
                    Text('Vstupenky'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'interactive_map',
                child: Row(
                  children: [
                    Icon(Icons.map_outlined, color: Color(0xFF2196F3)),
                    SizedBox(width: 12),
                    Text('Interaktivní mapa'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'offline_manager',
                child: Row(
                  children: [
                    Icon(Icons.offline_bolt, color: Color(0xFF9C27B0)),
                    SizedBox(width: 12),
                    Text('Offline režim'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'camera',
                child: Row(
                  children: [
                    Icon(Icons.camera_alt, color: Color(0xFF4CAF50)),
                    SizedBox(width: 12),
                    Text('Kamera'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'personalization',
                child: Row(
                  children: [
                    Icon(Icons.person, color: Color(0xFF9C27B0)),
                    SizedBox(width: 12),
                    Text('Personalizace'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'advanced_settings',
                child: Row(
                  children: [
                    Icon(Icons.settings_applications, color: Color(0xFF607D8B)),
                    SizedBox(width: 12),
                    Text('Pokročilá nastavení'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _screens[_currentIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF006994).withValues(alpha: 0.1),
              const Color(0xFF2E8B8B).withValues(alpha: 0.05),
            ],
          ),
        ),
        child: CustomPaint(
          painter: WatercolorBottomNavPainter(),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: const Color(0xFF006994),
            unselectedItemColor: const Color(0xFF666666),
            selectedLabelStyle: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: GoogleFonts.inter(
              fontSize: 11,
              fontWeight: FontWeight.w400,
            ),
            items: _navItems,
          ),
        ),
      ),
    );
  }
}

// Watercolor painter pro hlavní navigaci
class WatercolorNavigationPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2;

    // Jemné watercolor vlny v navigaci
    final path1 = Path();
    path1.moveTo(0, size.height * 0.4);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.2,
      size.width * 0.5,
      size.height * 0.5,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.8,
      size.width,
      size.height * 0.3,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.6);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.3,
      size.width * 0.7,
      size.height * 0.7,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.9,
      size.width,
      size.height * 0.5,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.1);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Watercolor painter pro bottom navigation
class WatercolorBottomNavPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemné watercolor vlny v bottom navigation
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.1,
      size.width * 0.5,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.7,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, 0);
    path1.lineTo(0, 0);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.1);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.6);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.8,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.9,
      size.width,
      size.height * 0.5,
    );
    path2.lineTo(size.width, 0);
    path2.lineTo(0, 0);
    path2.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
