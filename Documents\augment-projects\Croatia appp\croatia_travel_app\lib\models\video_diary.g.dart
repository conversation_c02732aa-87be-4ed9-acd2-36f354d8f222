// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_diary.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VideoDiary _$VideoDiaryFromJson(Map<String, dynamic> json) => VideoDiary(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      filePath: json['filePath'] as String,
      thumbnailPath: json['thumbnailPath'] as String?,
      duration: Duration(microseconds: (json['duration'] as num).toInt()),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      fileSize: (json['fileSize'] as num).toInt(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      location: json['location'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      quality: $enumDecodeNullable(_$VideoQualityEnumMap, json['quality']) ??
          VideoQuality.medium,
      orientation:
          $enumDecodeNullable(_$VideoOrientationEnumMap, json['orientation']) ??
              VideoOrientation.landscape,
      width: (json['width'] as num).toInt(),
      height: (json['height'] as num).toInt(),
      frameRate: (json['frameRate'] as num?)?.toDouble() ?? 30.0,
      hasAudio: json['hasAudio'] as bool? ?? true,
      isFavorite: json['isFavorite'] as bool? ?? false,
      isProcessed: json['isProcessed'] as bool? ?? false,
      edits: (json['edits'] as List<dynamic>?)
              ?.map((e) => VideoEdit.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      musicTrack: json['musicTrack'] as String?,
      filters: (json['filters'] as List<dynamic>?)
              ?.map((e) => VideoFilter.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$VideoDiaryToJson(VideoDiary instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'filePath': instance.filePath,
      'thumbnailPath': instance.thumbnailPath,
      'duration': instance.duration.inMicroseconds,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'fileSize': instance.fileSize,
      'tags': instance.tags,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'quality': _$VideoQualityEnumMap[instance.quality]!,
      'orientation': _$VideoOrientationEnumMap[instance.orientation]!,
      'width': instance.width,
      'height': instance.height,
      'frameRate': instance.frameRate,
      'hasAudio': instance.hasAudio,
      'isFavorite': instance.isFavorite,
      'isProcessed': instance.isProcessed,
      'edits': instance.edits.map((e) => e.toJson()).toList(),
      'musicTrack': instance.musicTrack,
      'filters': instance.filters.map((e) => e.toJson()).toList(),
    };

const _$VideoQualityEnumMap = {
  VideoQuality.low: 'low',
  VideoQuality.medium: 'medium',
  VideoQuality.high: 'high',
  VideoQuality.ultra: 'ultra',
};

const _$VideoOrientationEnumMap = {
  VideoOrientation.portrait: 'portrait',
  VideoOrientation.landscape: 'landscape',
  VideoOrientation.square: 'square',
};

VideoEdit _$VideoEditFromJson(Map<String, dynamic> json) => VideoEdit(
      id: json['id'] as String,
      type: $enumDecode(_$VideoEditTypeEnumMap, json['type']),
      startTime: Duration(microseconds: (json['startTime'] as num).toInt()),
      endTime: Duration(microseconds: (json['endTime'] as num).toInt()),
      parameters: json['parameters'] as Map<String, dynamic>,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$VideoEditToJson(VideoEdit instance) => <String, dynamic>{
      'id': instance.id,
      'type': _$VideoEditTypeEnumMap[instance.type]!,
      'startTime': instance.startTime.inMicroseconds,
      'endTime': instance.endTime.inMicroseconds,
      'parameters': instance.parameters,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$VideoEditTypeEnumMap = {
  VideoEditType.trim: 'trim',
  VideoEditType.cut: 'cut',
  VideoEditType.merge: 'merge',
  VideoEditType.speed: 'speed',
  VideoEditType.transition: 'transition',
  VideoEditType.textOverlay: 'text_overlay',
  VideoEditType.audioOverlay: 'audio_overlay',
};

VideoFilter _$VideoFilterFromJson(Map<String, dynamic> json) => VideoFilter(
      id: json['id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$VideoFilterTypeEnumMap, json['type']),
      intensity: (json['intensity'] as num?)?.toDouble() ?? 1.0,
      settings: json['settings'] as Map<String, dynamic>? ?? const {},
      isEnabled: json['isEnabled'] as bool? ?? true,
    );

Map<String, dynamic> _$VideoFilterToJson(VideoFilter instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$VideoFilterTypeEnumMap[instance.type]!,
      'intensity': instance.intensity,
      'settings': instance.settings,
      'isEnabled': instance.isEnabled,
    };

const _$VideoFilterTypeEnumMap = {
  VideoFilterType.colorCorrection: 'color_correction',
  VideoFilterType.vintage: 'vintage',
  VideoFilterType.blackWhite: 'black_white',
  VideoFilterType.sepia: 'sepia',
  VideoFilterType.blur: 'blur',
  VideoFilterType.sharpen: 'sharpen',
  VideoFilterType.croatiaSunset: 'croatia_sunset',
  VideoFilterType.adriaticBlue: 'adriatic_blue',
  VideoFilterType.mediterranean: 'mediterranean',
};

T $enumDecode<T>(
  Map<T, Object> enumValues,
  Object? source, {
  T? unknownValue,
}) {
  if (source == null) {
    throw ArgumentError(
      'A value must be provided. Supported values: '
      '${enumValues.values.join(', ')}',
    );
  }

  return enumValues.entries.singleWhere(
    (e) => e.value == source,
    orElse: () {
      if (unknownValue == null) {
        throw ArgumentError(
          '`$source` is not one of the supported values: '
          '${enumValues.values.join(', ')}',
        );
      }
      return MapEntry(unknownValue, enumValues.values.first);
    },
  ).key;
}

T? $enumDecodeNullable<T>(
  Map<T, Object> enumValues,
  Object? source, {
  T? unknownValue,
}) {
  if (source == null) {
    return null;
  }
  return $enumDecode<T>(enumValues, source, unknownValue: unknownValue);
}
