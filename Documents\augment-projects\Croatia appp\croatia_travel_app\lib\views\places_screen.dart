import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/place.dart';
import '../services/location_service.dart';
import '../widgets/place_card.dart';
import '../widgets/filter_widget.dart';

class PlacesScreen extends StatefulWidget {
  const PlacesScreen({super.key});

  @override
  State<PlacesScreen> createState() => _PlacesScreenState();
}

class _PlacesScreenState extends State<PlacesScreen> {
  List<Place> _allPlaces = [];
  List<Place> _filteredPlaces = [];
  String _searchQuery = '';
  String _selectedRegion = 'all';
  PlaceType? _selectedType;
  bool _showVisitedOnly = false;
  bool _sortByDistance = false;

  final LocationService _locationService = LocationService();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadPlaces();
  }

  Future<void> _loadPlaces() async {
    // Načtení míst z databáze - zde simulace dat
    _allPlaces = [
      Place(
        id: '1',
        name: 'Dubrovník - Staré město',
        description:
            'Historické centrum Dubrovníku s krásnými hradbami a úzkými uličkami. UNESCO světové dědictví.',
        latitude: 42.6407,
        longitude: 18.1077,
        region: 'dalmatia',
        type: PlaceType.monument,
        images: ['dubrovnik1.jpg', 'dubrovnik2.jpg'],
        isVisited: true,
        visitedDate: DateTime.now().subtract(const Duration(days: 5)),
        rating: 5.0,
        tags: ['UNESCO', 'historie', 'hradby', 'Game of Thrones'],
        additionalInfo: {
          'opening_hours': '24/7',
          'entrance_fee': '30 EUR',
          'best_time': 'Ráno nebo večer',
        },
      ),
      Place(
        id: '2',
        name: 'Plitvická jezera',
        description:
            'Národní park s 16 terasovitými jezery spojenými vodopády.',
        latitude: 44.8654,
        longitude: 15.5820,
        region: 'lika',
        type: PlaceType.park,
        images: ['plitvice1.jpg', 'plitvice2.jpg'],
        isVisited: false,
        rating: 4.9,
        tags: ['národní park', 'příroda', 'vodopády', 'UNESCO'],
        additionalInfo: {
          'opening_hours': '7:00-19:00',
          'entrance_fee': '40 EUR',
          'duration': '4-8 hodin',
        },
      ),
      Place(
        id: '3',
        name: 'Zlatni Rat - Bol',
        description: 'Nejslavnější pláž v Chorvatsku na ostrově Brač.',
        latitude: 43.2567,
        longitude: 16.6378,
        region: 'dalmatia',
        type: PlaceType.beach,
        images: ['zlatni_rat1.jpg'],
        isVisited: false,
        rating: 4.7,
        tags: ['pláž', 'windsurfing', 'ostrov Brač'],
        additionalInfo: {
          'best_time': 'Červen-září',
          'activities': 'Windsurfing, kitesurfing',
        },
      ),
      Place(
        id: '4',
        name: 'Rovinj',
        description: 'Romantické město v Istrii s benátskou architekturou.',
        latitude: 45.0811,
        longitude: 13.6387,
        region: 'istria',
        type: PlaceType.monument,
        images: ['rovinj1.jpg'],
        isVisited: true,
        visitedDate: DateTime.now().subtract(const Duration(days: 15)),
        rating: 4.6,
        tags: ['Istrie', 'romantika', 'benátská architektura'],
      ),
      Place(
        id: '5',
        name: 'Krka National Park',
        description: 'Národní park se sedmi vodopády a možností koupání.',
        latitude: 43.8069,
        longitude: 15.9614,
        region: 'dalmatia',
        type: PlaceType.park,
        images: ['krka1.jpg'],
        isVisited: false,
        rating: 4.5,
        tags: ['národní park', 'vodopády', 'koupání'],
      ),
    ];

    _filteredPlaces = List.from(_allPlaces);
    setState(() {});
  }

  void _filterPlaces() {
    List<Place> filtered = List.from(_allPlaces);

    // Filtrování podle vyhledávání
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((place) {
        return place.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            place.description.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            place.tags.any(
              (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
            );
      }).toList();
    }

    // Filtrování podle regionu
    if (_selectedRegion != 'all') {
      filtered = filtered
          .where((place) => place.region == _selectedRegion)
          .toList();
    }

    // Filtrování podle typu
    if (_selectedType != null) {
      filtered = filtered
          .where((place) => place.type == _selectedType)
          .toList();
    }

    // Filtrování navštívených míst
    if (_showVisitedOnly) {
      filtered = filtered.where((place) => place.isVisited).toList();
    }

    // Řazení podle vzdálenosti
    if (_sortByDistance) {
      _sortPlacesByDistance(filtered);
    } else {
      // Řazení podle hodnocení
      filtered.sort((a, b) => (b.rating ?? 0).compareTo(a.rating ?? 0));
    }

    setState(() {
      _filteredPlaces = filtered;
    });
  }

  Future<void> _sortPlacesByDistance(List<Place> places) async {
    try {
      final currentPosition = await _locationService.getCurrentPosition();

      places.sort((a, b) {
        final distanceA = _locationService.calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          a.latitude,
          a.longitude,
        );
        final distanceB = _locationService.calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          b.latitude,
          b.longitude,
        );
        return distanceA.compareTo(distanceB);
      });
    } catch (e) {
      debugPrint('Chyba při řazení podle vzdálenosti: $e');
    }
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => FilterWidget(
        selectedRegion: _selectedRegion,
        selectedType: _selectedType,
        showVisitedOnly: _showVisitedOnly,
        sortByDistance: _sortByDistance,
        onRegionChanged: (region) {
          setState(() {
            _selectedRegion = region;
          });
          _filterPlaces();
        },
        onTypeChanged: (type) {
          setState(() {
            _selectedType = type;
          });
          _filterPlaces();
        },
        onVisitedOnlyChanged: (value) {
          setState(() {
            _showVisitedOnly = value;
          });
          _filterPlaces();
        },
        onSortByDistanceChanged: (value) {
          setState(() {
            _sortByDistance = value;
          });
          _filterPlaces();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Místa k návštěvě',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                const Color(0xFF006994).withValues(alpha: 0.9),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorPlacesHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _showFilterDialog,
              icon: const Icon(Icons.filter_list),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Vyhledávací pole s watercolor efektem
          Padding(
            padding: const EdgeInsets.all(16),
            child: Container(
              child: CustomPaint(
                painter: WatercolorSearchPainter(),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Vyhledat místa...',
                    hintStyle: GoogleFonts.inter(
                      color: const Color(0xFF666666),
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: const Color(0xFF006994),
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                              _filterPlaces();
                            },
                            icon: Icon(
                              Icons.clear,
                              color: const Color(0xFF666666),
                            ),
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: const Color(0xFF2E8B8B).withValues(alpha: 0.3),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: const Color(0xFF2E8B8B).withValues(alpha: 0.3),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: const Color(0xFF006994),
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.white.withValues(alpha: 0.9),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                    _filterPlaces();
                  },
                ),
              ),
            ),
          ),

          // Statistiky s watercolor efektem
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Container(
              padding: const EdgeInsets.all(16),
              child: CustomPaint(
                painter: WatercolorStatsPainter(),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildWatercolorStat(
                        'Nalezeno',
                        '${_filteredPlaces.length}',
                        'míst',
                        const Color(0xFF006994),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: _buildWatercolorStat(
                        'Navštíveno',
                        '${_allPlaces.where((p) => p.isVisited).length}',
                        'míst',
                        const Color(0xFF2E8B8B),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: _buildWatercolorStat(
                        'Hodnocení',
                        '${(_allPlaces.map((p) => p.rating ?? 0).reduce((a, b) => a + b) / _allPlaces.length).toStringAsFixed(1)}',
                        'průměr',
                        const Color(0xFFFF6B35),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Seznam míst
          Expanded(
            child: _filteredPlaces.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.location_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Žádná místa nenalezena',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Zkuste změnit filtry nebo vyhledávání',
                          style: TextStyle(color: Colors.grey[500]),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredPlaces.length,
                    itemBuilder: (context, index) {
                      final place = _filteredPlaces[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: PlaceCard(
                          place: place,
                          onTap: () => _navigateToPlaceDetail(place),
                          onVisitedToggle: () => _toggleVisited(place),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  void _navigateToPlaceDetail(Place place) {
    // Navigace na detail místa
    Navigator.pushNamed(context, '/places/${place.id}');
  }

  void _toggleVisited(Place place) {
    setState(() {
      final index = _allPlaces.indexWhere((p) => p.id == place.id);
      if (index != -1) {
        _allPlaces[index] = place.copyWith(
          isVisited: !place.isVisited,
          visitedDate: !place.isVisited ? DateTime.now() : null,
        );
      }
    });
    _filterPlaces();
  }

  // Watercolor statistika
  Widget _buildWatercolorStat(
    String label,
    String value,
    String unit,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: CustomPaint(
        painter: WatercolorPlacesStatPainter(color),
        child: Column(
          children: [
            Text(
              value,
              style: GoogleFonts.playfairDisplay(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              unit,
              style: GoogleFonts.inter(
                fontSize: 10,
                color: color.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2C2C2C),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

// Watercolor painters pro Places Screen
class WatercolorPlacesHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro Places header
    final path1 = Path();
    path1.moveTo(0, size.height * 0.4);
    path1.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.6,
      size.height * 0.5,
    );
    path1.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.8,
      size.width,
      size.height * 0.3,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.6);
    path2.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.3,
      size.width * 0.7,
      size.height * 0.7,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.9,
      size.width,
      size.height * 0.5,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFFFF6B35).withValues(alpha: 0.1);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSearchPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt kolem vyhledávacího pole
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.7,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.25,
      size.width * 0.9,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.5,
      size.width * 0.05,
      size.height * 0.2,
    );
    path.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorStatsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor pozadí pro statistiky
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.3);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.1,
      size.width * 0.9,
      size.height * 0.2,
    );
    path.lineTo(size.width * 0.85, size.height * 0.7);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.9,
      size.width * 0.15,
      size.height * 0.8,
    );
    path.close();

    paint.color = const Color(0xFF006994).withValues(alpha: 0.05);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorPlacesStatPainter extends CustomPainter {
  final Color color;

  WatercolorPlacesStatPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Malý watercolor kruh kolem statistiky
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 3.5;

    // Organický tvar kolem čísla
    final path = Path();
    for (int i = 0; i < 360; i += 25) {
      final angle = i * pi / 180;
      final variation = 0.6 + (sin(i * pi / 45) * 0.4);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.15);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
